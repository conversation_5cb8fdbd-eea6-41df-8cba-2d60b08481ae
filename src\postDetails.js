import { useParams } from "react-router-dom"
import { useContext } from 'react';
import { postsContext } from './contexts/postsContext';

export default function PostDetails() {
    const PostData = useContext(postsContext);
    const { postId } = useParams();

    const post = PostData.find((p) => {
        return (p.id === parseInt(postId))
    })
    return(
        <>
            <h1>Post Details</h1>
            <h3>{post.title}</h3>
            <p>{post.body}</p>
        </>
    )
}