{"tailwindCSS.includeLanguages": {"javascript": "javascript", "javascriptreact": "javascriptreact", "html": "html"}, "tailwindCSS.experimental.classRegex": [["className\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([^\"'`]*)"], ["className\\s*=\\s*{[\"'`]([^\"'`]*)[\"'`]}", "([^\"'`]*)"], ["class\\s*=\\s*[\"'`]([^\"'`]*)[\"'`]", "([^\"'`]*)"]], "editor.quickSuggestions": {"strings": true}, "css.validate": false, "tailwindCSS.emmetCompletions": true, "tailwindCSS.validate": true, "tailwindCSS.lint.cssConflict": "warning", "tailwindCSS.lint.invalidApply": "error", "tailwindCSS.lint.invalidScreen": "error", "tailwindCSS.lint.invalidVariant": "error", "tailwindCSS.lint.invalidConfigPath": "error", "tailwindCSS.lint.invalidTailwindDirective": "error", "tailwindCSS.lint.recommendedVariantOrder": "warning"}