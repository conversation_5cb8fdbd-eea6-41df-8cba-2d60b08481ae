import logo from './logo.svg';
import './App.css';
import {Routes, Route, Link} from 'react-router-dom';
import Posts from './posts';
import PostDetails from './postDetails';
import {postsContext} from './contexts/postsContext';
import Button from '@mui/material/Button';
import DeleteIcon from '@mui/icons-material/Delete';
import { Avatar } from '@mui/material';
import { brown } from '@mui/material/colors';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import ExpandLess from '@mui/icons-material/ExpandLess';
import { Switch } from '@mui/material';
import { Container } from '@mui/material';

// Import the posts data
const postsArr = [
    {id:1, title: 'Post 1', body: 'This is post 1'},
    {id:2, title: 'Post 2', body: 'This is post 2'},
    {id:3, title: 'Post 3', body: 'This is post 3'},
    {id:4, title: 'Post 4', body: 'This is post 4'},
]
let divWidth = "100px";
function handleChange(){
    if(divWidth === "100px"){
        divWidth = "200px";
    }else{
        divWidth = "100px";
    }
}
function App() {
  function stringSplit(n){
    return(
      n.split(" ")[0][0] + n.split(" ")[1][0]
    )
  }
  return (
  <postsContext.Provider value={postsArr}>

        <Avatar sx={{bgcolor: brown[500]}} >{stringSplit("Mohamed Ayman")}</Avatar>
        <Link to="/"> <Button variant='contained' startIcon={<DeleteIcon />}> Home</Button> </Link>
        <Link to="/posts"><button>Posts</button></Link>
        <Routes>
          <Route path='/' element={
            <header className="App-header">
              <img src={logo} className="App-logo" alt="logo" />
              <Accordion style={{width: "60%"}}>
                <AccordionSummary expandIcon={<ExpandLess />}>
                  expanded
                </AccordionSummary>
                <AccordionDetails>
                  Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse
                  malesuada lacus ex, sit amet blandit leo lobortis eget.
                </AccordionDetails>
              </Accordion>
              <Accordion style={{width: "60%"}}>
                <AccordionSummary expandIcon={<ExpandLess />}>
                  expanded
                </AccordionSummary>
                <AccordionDetails style={{width:"100%"}}>
                  <Container maxWidth="xl">
                    <Switch color={"success"} onChange={{handleChange}/>
                  </Container>
                </AccordionDetails>
              </Accordion>
              <div style={{width:{divWidth}, height:"100px" , background:"orange"}}></div>
              <p>
                Edit <code>src/App.js</code> and save to reload.
              </p>
              <a
                className="App-link"
                href="https://reactjs.org"
                target="_blank"
                rel="noopener noreferrer"
              >
                Learn React
              </a>
            </header>
          } />
          <Route path='/posts' element={<Posts /> } />
          <Route path='/postDetails/:postId' element={<PostDetails /> } />
          <Route path="*" element={<h1>404 Not Found</h1>} />
        </Routes>
      </postsContext.Provider>
  );
}

export default App;
