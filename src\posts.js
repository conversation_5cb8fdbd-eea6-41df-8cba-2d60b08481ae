import { Link } from 'react-router-dom'
import { useContext } from 'react';
import { postsContext } from './contexts/postsContext';
export default function Posts() {
    const posts = useContext(postsContext);
    const postsList = posts.map((post,i) => {
        return(
            <Link key={i} to={`/postDetails/${post.id}`}>
                <div className={"bg-blue-500 m-2 p-2 rounded-lg"}>
                    <h1>{post.title}</h1>
                </div>
            </Link>
        )
    })
    return(
        <>
            {postsList}
        </>
    )
}
